<!--
  图标展示组件
  用于展示和测试 Iconify 图标系统
-->

<template>
  <div class="icon-showcase">
    <h3>🎨 Iconify 图标展示</h3>
    
    <!-- 控制图标组 -->
    <div class="icon-group">
      <h4>控制图标</h4>
      <div class="icon-grid">
        <div class="icon-item" v-for="icon in controlIcons" :key="icon.name">
          <Icon :icon="icon.icon" class="showcase-icon" />
          <span class="icon-name">{{ icon.name }}</span>
          <code class="icon-code">{{ icon.icon }}</code>
        </div>
      </div>
    </div>

    <!-- 状态图标组 -->
    <div class="icon-group">
      <h4>状态图标</h4>
      <div class="icon-grid">
        <div class="icon-item" v-for="icon in statusIcons" :key="icon.name">
          <Icon :icon="icon.icon" class="showcase-icon" />
          <span class="icon-name">{{ icon.name }}</span>
          <code class="icon-code">{{ icon.icon }}</code>
        </div>
      </div>
    </div>

    <!-- 动作图标组 -->
    <div class="icon-group">
      <h4>动作图标</h4>
      <div class="icon-grid">
        <div class="icon-item" v-for="icon in actionIcons" :key="icon.name">
          <Icon :icon="icon.icon" class="showcase-icon" />
          <span class="icon-name">{{ icon.name }}</span>
          <code class="icon-code">{{ icon.icon }}</code>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'

// 控制图标
const controlIcons = [
  { name: '设置', icon: 'fluent:settings-24-regular' },
  { name: '设置(填充)', icon: 'fluent:settings-24-filled' },
  { name: '表情', icon: 'fluent:emoji-24-regular' },
  { name: '播放', icon: 'fluent:play-24-regular' },
  { name: '暂停', icon: 'fluent:pause-24-regular' },
  { name: '停止', icon: 'fluent:stop-24-regular' },
  { name: '音频', icon: 'fluent:speaker-2-24-regular' },
  { name: '静音', icon: 'fluent:speaker-mute-24-regular' },
  { name: '麦克风', icon: 'fluent:mic-24-regular' },
  { name: '麦克风关闭', icon: 'fluent:mic-off-24-regular' },
  { name: '置顶', icon: 'fluent:pin-24-regular' },
  { name: '置顶(填充)', icon: 'fluent:pin-24-filled' },
  { name: '最小化', icon: 'fluent:minimize-24-regular' },
  { name: '关闭', icon: 'fluent:dismiss-24-regular' }
]

// 状态图标
const statusIcons = [
  { name: '加载中', icon: 'fluent:spinner-ios-20-regular' },
  { name: '成功', icon: 'fluent:checkmark-circle-24-regular' },
  { name: '错误', icon: 'fluent:error-circle-24-regular' },
  { name: '警告', icon: 'fluent:warning-24-regular' },
  { name: '信息', icon: 'fluent:info-24-regular' },
  { name: '帮助', icon: 'fluent:question-circle-24-regular' }
]

// 动作图标
const actionIcons = [
  { name: '刷新', icon: 'fluent:arrow-clockwise-24-regular' },
  { name: '重置', icon: 'fluent:arrow-reset-24-regular' },
  { name: '编辑', icon: 'fluent:edit-24-regular' },
  { name: '删除', icon: 'fluent:delete-24-regular' },
  { name: '复制', icon: 'fluent:copy-24-regular' },
  { name: '粘贴', icon: 'fluent:clipboard-paste-24-regular' },
  { name: '保存', icon: 'fluent:save-24-regular' },
  { name: '下载', icon: 'fluent:arrow-download-24-regular' },
  { name: '上传', icon: 'fluent:arrow-upload-24-regular' },
  { name: '搜索', icon: 'fluent:search-24-regular' }
]
</script>

<style scoped>
.icon-showcase {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.icon-showcase h3 {
  margin: 0 0 20px 0;
  color: #333;
  text-align: center;
}

.icon-group {
  margin-bottom: 24px;
}

.icon-group h4 {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 4px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  cursor: pointer;
}

.icon-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  transform: translateY(-1px);
}

.showcase-icon {
  font-size: 24px;
  color: #6c757d;
  margin-bottom: 6px;
  transition: color 0.2s ease;
}

.icon-item:hover .showcase-icon {
  color: #007bff;
}

.icon-name {
  font-size: 11px;
  color: #333;
  margin-bottom: 4px;
  text-align: center;
  font-weight: 500;
}

.icon-code {
  font-size: 9px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.icon-item:hover .icon-code {
  color: #007bff;
  background: #e7f3ff;
}
</style>
