{"name": "live2d-desktop-pet", "private": true, "version": "1.0.0", "description": "Live2D 桌面宠物应用", "main": "electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "node start-electron.js", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && node start-electron.js\"", "electron:build": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "pet": "node start-electron.js", "pet:dev": "npm run dev & sleep 3 && npm run pet"}, "dependencies": {"pixi-live2d-display": "^0.4.0", "pixi.js": "^6.5.0", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.0", "electron": "^37.2.4", "vite": "^5.4.0"}, "packageManager": "pnpm@9.0.6+sha1.648f6014eb363abb36618f2ba59282a9eeb3e879"}